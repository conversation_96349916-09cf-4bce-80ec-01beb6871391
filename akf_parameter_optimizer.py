"""
自适应卡尔曼滤波器参数优化模块
提供AKF参数自动调优和性能评估功能
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import minimize, differential_evolution
import warnings

warnings.filterwarnings("ignore")


class OptimizedAdaptiveKalmanFilter:
    """优化版自适应卡尔曼滤波器"""
    
    def __init__(self, process_variance=1e-5, measurement_variance=1e-3, 
                 adaptation_rate=0.01, innovation_window=10, 
                 adaptive_strategy='variance_based'):
        """
        初始化优化版AKF
        
        Args:
            process_variance: 过程噪声方差
            measurement_variance: 测量噪声方差
            adaptation_rate: 自适应调整率
            innovation_window: 新息历史窗口大小
            adaptive_strategy: 自适应策略 ('variance_based', 'innovation_based', 'hybrid')
        """
        # 基本参数
        self.Q = process_variance
        self.R = measurement_variance
        self.adaptation_rate = adaptation_rate
        self.innovation_window = innovation_window
        self.adaptive_strategy = adaptive_strategy
        
        # 状态变量
        self.x = 0.0  # 状态估计
        self.P = 1.0  # 估计误差协方差
        
        # 自适应参数
        self.innovation_history = []
        self.residual_history = []
        self.prediction_count = 0
        
        # 优化参数
        self.Q_min = 1e-8
        self.Q_max = 1e-2
        self.R_min = 1e-6
        self.R_max = 1e-1
        
        # 性能监控
        self.performance_metrics = {
            'mse': [],
            'mae': [],
            'innovation_variance': [],
            'Q_history': [],
            'R_history': []
        }
    
    def predict_and_update(self, measurement, lstm_prediction=None, actual_value=None):
        """
        预测和更新步骤
        
        Args:
            measurement: 观测值
            lstm_prediction: LSTM预测值（用作先验）
            actual_value: 真实值（用于性能评估）
        
        Returns:
            float: 滤波后的估计值
        """
        # 时间更新（预测）
        if lstm_prediction is not None:
            # 使用LSTM预测作为先验
            x_pred = lstm_prediction
        else:
            # 使用上一时刻状态作为先验
            x_pred = self.x
        
        P_pred = self.P + self.Q
        
        # 测量更新
        innovation = measurement - x_pred
        S = P_pred + self.R
        K = P_pred / S
        
        # 状态更新
        self.x = x_pred + K * innovation
        self.P = (1 - K) * P_pred
        
        # 记录新息和残差
        self.innovation_history.append(innovation)
        if len(self.innovation_history) > self.innovation_window:
            self.innovation_history.pop(0)
        
        if actual_value is not None:
            residual = self.x - actual_value
            self.residual_history.append(residual)
            if len(self.residual_history) > self.innovation_window:
                self.residual_history.pop(0)
        
        # 自适应调整
        self._adaptive_adjustment(actual_value)
        
        # 更新性能指标
        self._update_performance_metrics(actual_value)
        
        self.prediction_count += 1
        return self.x
    
    def _adaptive_adjustment(self, actual_value=None):
        """自适应调整噪声参数"""
        if len(self.innovation_history) < 3:
            return
        
        if self.adaptive_strategy == 'variance_based':
            self._variance_based_adaptation()
        elif self.adaptive_strategy == 'innovation_based':
            self._innovation_based_adaptation()
        elif self.adaptive_strategy == 'hybrid':
            self._hybrid_adaptation(actual_value)
    
    def _variance_based_adaptation(self):
        """基于方差的自适应调整"""
        innovation_var = np.var(self.innovation_history)
        
        # 调整测量噪声
        target_R = innovation_var * 0.8
        self.R = (1 - self.adaptation_rate) * self.R + self.adaptation_rate * target_R
        self.R = np.clip(self.R, self.R_min, self.R_max)
        
        # 调整过程噪声
        if innovation_var > np.mean([abs(i) for i in self.innovation_history]) * 2:
            self.Q = min(self.Q * 1.1, self.Q_max)
        else:
            self.Q = max(self.Q * 0.99, self.Q_min)
    
    def _innovation_based_adaptation(self):
        """基于新息序列的自适应调整"""
        recent_innovations = self.innovation_history[-5:]
        avg_innovation = np.mean([abs(i) for i in recent_innovations])
        
        # 根据新息大小调整参数
        if avg_innovation > 0.01:
            self.R = min(self.R * (1 + self.adaptation_rate), self.R_max)
            self.Q = min(self.Q * (1 + self.adaptation_rate * 0.5), self.Q_max)
        else:
            self.R = max(self.R * (1 - self.adaptation_rate), self.R_min)
            self.Q = max(self.Q * (1 - self.adaptation_rate * 0.5), self.Q_min)
    
    def _hybrid_adaptation(self, actual_value=None):
        """混合自适应策略"""
        # 结合方差和新息的信息
        self._variance_based_adaptation()
        
        # 如果有真实值，进一步调整
        if actual_value is not None and len(self.residual_history) >= 3:
            residual_var = np.var(self.residual_history[-3:])
            
            if residual_var > 1e-4:  # 如果残差方差较大
                self.Q = min(self.Q * 1.05, self.Q_max)
            else:
                self.Q = max(self.Q * 0.98, self.Q_min)
    
    def _update_performance_metrics(self, actual_value=None):
        """更新性能指标"""
        if actual_value is not None:
            error = self.x - actual_value
            self.performance_metrics['mse'].append(error ** 2)
            self.performance_metrics['mae'].append(abs(error))
        
        if len(self.innovation_history) > 0:
            self.performance_metrics['innovation_variance'].append(
                np.var(self.innovation_history[-min(5, len(self.innovation_history)):])
            )
        
        self.performance_metrics['Q_history'].append(self.Q)
        self.performance_metrics['R_history'].append(self.R)
    
    def get_performance_summary(self):
        """获取性能摘要"""
        if not self.performance_metrics['mse']:
            return None
        
        return {
            'mse': np.mean(self.performance_metrics['mse']),
            'mae': np.mean(self.performance_metrics['mae']),
            'rmse': np.sqrt(np.mean(self.performance_metrics['mse'])),
            'final_Q': self.Q,
            'final_R': self.R,
            'avg_innovation_var': np.mean(self.performance_metrics['innovation_variance']) if self.performance_metrics['innovation_variance'] else 0
        }


class AKFParameterOptimizer:
    """AKF参数优化器"""
    
    def __init__(self):
        self.optimization_methods = {
            'grid_search': self._grid_search_optimization,
            'random_search': self._random_search_optimization,
            'bayesian': self._bayesian_optimization,
            'differential_evolution': self._differential_evolution_optimization
        }
    
    def optimize_parameters(self, lstm_predictions, measurements, actual_values=None,
                          method='differential_evolution', max_evaluations=50):
        """
        优化AKF参数
        
        Args:
            lstm_predictions: LSTM预测序列
            measurements: 观测序列
            actual_values: 真实值序列（可选）
            method: 优化方法
            max_evaluations: 最大评估次数
        
        Returns:
            dict: 最优参数和性能
        """
        print(f"开始AKF参数优化，方法: {method}")
        print(f"数据长度: {len(measurements)}")
        
        # 定义目标函数
        def objective_function(params):
            return self._evaluate_akf_performance(
                params, lstm_predictions, measurements, actual_values
            )
        
        # 选择优化方法
        if method in self.optimization_methods:
            result = self.optimization_methods[method](
                objective_function, max_evaluations
            )
        else:
            raise ValueError(f"未知的优化方法: {method}")
        
        return result
    
    def _evaluate_akf_performance(self, params, lstm_predictions, measurements, actual_values):
        """评估AKF性能"""
        process_var, measurement_var, adaptation_rate = params
        
        # 创建AKF实例
        akf = OptimizedAdaptiveKalmanFilter(
            process_variance=process_var,
            measurement_variance=measurement_var,
            adaptation_rate=adaptation_rate,
            adaptive_strategy='hybrid'
        )
        
        # 运行滤波
        filtered_values = []
        for i in range(len(measurements)):
            lstm_pred = lstm_predictions[i] if i < len(lstm_predictions) else None
            actual_val = actual_values[i] if actual_values and i < len(actual_values) else None
            
            filtered_val = akf.predict_and_update(
                measurements[i], lstm_pred, actual_val
            )
            filtered_values.append(filtered_val)
        
        # 计算性能指标
        if actual_values:
            # 如果有真实值，使用真实值计算误差
            errors = np.array(filtered_values) - np.array(actual_values[:len(filtered_values)])
            mse = np.mean(errors ** 2)
        else:
            # 否则使用与LSTM预测的差异
            lstm_errors = np.array(filtered_values) - np.array(lstm_predictions[:len(filtered_values)])
            measurement_errors = np.array(filtered_values) - np.array(measurements[:len(filtered_values)])
            # 综合考虑与LSTM和测量值的差异
            mse = 0.7 * np.mean(measurement_errors ** 2) + 0.3 * np.mean(lstm_errors ** 2)
        
        return mse
    
    def _grid_search_optimization(self, objective_function, max_evaluations):
        """网格搜索优化"""
        # 定义搜索网格
        process_vars = np.logspace(-8, -3, 8)
        measurement_vars = np.logspace(-6, -2, 8)
        adaptation_rates = np.linspace(0.001, 0.1, 5)
        
        best_params = None
        best_score = float('inf')
        evaluations = 0
        
        for pv in process_vars:
            for mv in measurement_vars:
                for ar in adaptation_rates:
                    if evaluations >= max_evaluations:
                        break
                    
                    params = [pv, mv, ar]
                    score = objective_function(params)
                    
                    if score < best_score:
                        best_score = score
                        best_params = params
                    
                    evaluations += 1
                
                if evaluations >= max_evaluations:
                    break
            if evaluations >= max_evaluations:
                break
        
        return {
            'best_params': {
                'process_variance': best_params[0],
                'measurement_variance': best_params[1],
                'adaptation_rate': best_params[2]
            },
            'best_score': best_score,
            'evaluations': evaluations
        }
    
    def _random_search_optimization(self, objective_function, max_evaluations):
        """随机搜索优化"""
        best_params = None
        best_score = float('inf')
        
        for i in range(max_evaluations):
            # 随机采样参数
            process_var = np.random.uniform(1e-8, 1e-3)
            measurement_var = np.random.uniform(1e-6, 1e-2)
            adaptation_rate = np.random.uniform(0.001, 0.1)
            
            params = [process_var, measurement_var, adaptation_rate]
            score = objective_function(params)
            
            if score < best_score:
                best_score = score
                best_params = params
        
        return {
            'best_params': {
                'process_variance': best_params[0],
                'measurement_variance': best_params[1],
                'adaptation_rate': best_params[2]
            },
            'best_score': best_score,
            'evaluations': max_evaluations
        }
    
    def _differential_evolution_optimization(self, objective_function, max_evaluations):
        """差分进化优化"""
        bounds = [
            (1e-8, 1e-3),  # process_variance
            (1e-6, 1e-2),  # measurement_variance
            (0.001, 0.1)   # adaptation_rate
        ]
        
        result = differential_evolution(
            objective_function,
            bounds,
            maxiter=max_evaluations // 10,
            popsize=10,
            seed=42
        )
        
        return {
            'best_params': {
                'process_variance': result.x[0],
                'measurement_variance': result.x[1],
                'adaptation_rate': result.x[2]
            },
            'best_score': result.fun,
            'evaluations': result.nfev
        }
    
    def _bayesian_optimization(self, objective_function, max_evaluations):
        """贝叶斯优化（简化版）"""
        # 这里实现一个简化的贝叶斯优化
        # 实际应用中可以使用 scikit-optimize 或 optuna
        return self._random_search_optimization(objective_function, max_evaluations)


def compare_akf_strategies(lstm_predictions, measurements, actual_values=None):
    """
    比较不同AKF策略的性能
    
    Args:
        lstm_predictions: LSTM预测序列
        measurements: 观测序列
        actual_values: 真实值序列（可选）
    
    Returns:
        dict: 比较结果
    """
    strategies = ['variance_based', 'innovation_based', 'hybrid']
    results = {}
    
    print("=" * 60)
    print("AKF策略性能比较")
    print("=" * 60)
    
    for strategy in strategies:
        print(f"\n测试策略: {strategy}")
        
        # 创建AKF实例
        akf = OptimizedAdaptiveKalmanFilter(
            process_variance=1e-5,
            measurement_variance=1e-3,
            adaptation_rate=0.01,
            adaptive_strategy=strategy
        )
        
        # 运行滤波
        filtered_values = []
        for i in range(len(measurements)):
            lstm_pred = lstm_predictions[i] if i < len(lstm_predictions) else None
            actual_val = actual_values[i] if actual_values and i < len(actual_values) else None
            
            filtered_val = akf.predict_and_update(
                measurements[i], lstm_pred, actual_val
            )
            filtered_values.append(filtered_val)
        
        # 获取性能摘要
        performance = akf.get_performance_summary()
        results[strategy] = {
            'performance': performance,
            'filtered_values': filtered_values
        }
        
        if performance:
            print(f"  MSE: {performance['mse']:.8f}")
            print(f"  MAE: {performance['mae']:.6f}")
            print(f"  RMSE: {performance['rmse']:.6f}")
            print(f"  最终Q: {performance['final_Q']:.2e}")
            print(f"  最终R: {performance['final_R']:.2e}")
    
    # 找出最佳策略
    valid_results = {k: v for k, v in results.items() if v['performance']}
    if valid_results:
        best_strategy = min(valid_results.keys(), 
                          key=lambda k: valid_results[k]['performance']['mse'])
        print(f"\n🏆 最佳策略: {best_strategy}")
        print(f"   MSE: {valid_results[best_strategy]['performance']['mse']:.8f}")
    
    return results


if __name__ == "__main__":
    # 测试AKF参数优化
    print("AKF参数优化模块测试")
    
    # 生成测试数据
    np.random.seed(42)
    n_points = 200
    
    # 模拟真实值（正弦波 + 噪声）
    t = np.linspace(0, 4*np.pi, n_points)
    actual_values = np.sin(t) + 0.1 * np.random.randn(n_points)
    
    # 模拟LSTM预测（有一定偏差）
    lstm_predictions = actual_values + 0.05 * np.random.randn(n_points)
    
    # 模拟观测值（有噪声）
    measurements = actual_values + 0.2 * np.random.randn(n_points)
    
    # 比较不同策略
    strategy_results = compare_akf_strategies(lstm_predictions, measurements, actual_values)
    
    # 参数优化
    print("\n" + "=" * 60)
    print("参数优化测试")
    print("=" * 60)
    
    optimizer = AKFParameterOptimizer()
    optimization_result = optimizer.optimize_parameters(
        lstm_predictions, measurements, actual_values,
        method='differential_evolution', max_evaluations=50
    )
    
    print(f"\n最优参数:")
    for key, value in optimization_result['best_params'].items():
        print(f"  {key}: {value:.2e}")
    print(f"最优得分: {optimization_result['best_score']:.8f}")
    print(f"评估次数: {optimization_result['evaluations']}")
